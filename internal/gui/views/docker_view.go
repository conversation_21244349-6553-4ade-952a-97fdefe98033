// Package views provides the Docker module UI with Material Design 3 and cyberpunk styling
package views

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// DockerView represents the Docker module interface
type DockerView struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *container.Scroll
	content   fyne.CanvasObject

	// Docker components
	containerList *widget.List
	imageList     *widget.List
	volumeList    *widget.List
	networkList   *widget.List
	logViewer     *widget.Entry
	statusLabel   *widget.Label
	dockerStatus  *widget.Label

	// Current state
	containers        []DockerContainer
	images            []DockerImage
	volumes           []DockerVolume
	networks          []DockerNetwork
	selectedContainer int
	selectedImage     int
	currentContainer  string
}

// DockerContainer represents a Docker container
type DockerContainer struct {
	ID      string
	Name    string
	Image   string
	Status  string
	Ports   string
	Created time.Time
}

// DockerImage represents a Docker image
type DockerImage struct {
	ID      string
	Name    string
	Tag     string
	Size    string
	Created time.Time
}

// DockerVolume represents a Docker volume
type DockerVolume struct {
	Name       string
	Driver     string
	Mountpoint string
	Created    time.Time
}

// DockerNetwork represents a Docker network
type DockerNetwork struct {
	ID     string
	Name   string
	Driver string
	Scope  string
}

// NewDockerView creates a new Docker module view
func NewDockerView(cfg *config.Config, theme *cybertheme.CyberTheme) (*DockerView, error) {
	view := &DockerView{
		config: cfg,
		theme:  theme,
		containers: []DockerContainer{
			{ID: "abc123", Name: "web-app", Image: "nginx:latest", Status: "Running", Ports: "80:8080", Created: time.Now().Add(-2 * time.Hour)},
			{ID: "def456", Name: "database", Image: "postgres:13", Status: "Running", Ports: "5432:5432", Created: time.Now().Add(-1 * time.Hour)},
			{ID: "ghi789", Name: "redis-cache", Image: "redis:alpine", Status: "Stopped", Ports: "6379:6379", Created: time.Now().Add(-30 * time.Minute)},
		},
		images: []DockerImage{
			{ID: "img001", Name: "nginx", Tag: "latest", Size: "133MB", Created: time.Now().Add(-24 * time.Hour)},
			{ID: "img002", Name: "postgres", Tag: "13", Size: "314MB", Created: time.Now().Add(-12 * time.Hour)},
			{ID: "img003", Name: "redis", Tag: "alpine", Size: "32MB", Created: time.Now().Add(-6 * time.Hour)},
		},
		volumes: []DockerVolume{
			{Name: "postgres_data", Driver: "local", Mountpoint: "/var/lib/docker/volumes/postgres_data", Created: time.Now().Add(-24 * time.Hour)},
			{Name: "app_logs", Driver: "local", Mountpoint: "/var/lib/docker/volumes/app_logs", Created: time.Now().Add(-12 * time.Hour)},
		},
		networks: []DockerNetwork{
			{ID: "net001", Name: "bridge", Driver: "bridge", Scope: "local"},
			{ID: "net002", Name: "app-network", Driver: "bridge", Scope: "local"},
		},
		selectedContainer: -1,
		selectedImage:     -1,
	}

	// Create main content
	content := view.createContent()

	// Create scrollable container with enhanced responsive design
	scrollContainer := container.NewScroll(content)
	scrollContainer.SetMinSize(fyne.NewSize(600, 400))
	scrollContainer.Direction = container.ScrollBoth

	view.container = scrollContainer
	view.content = content

	return view, nil
}

// createContent creates the main Docker interface following fyne.md specifications
func (dv *DockerView) createContent() fyne.CanvasObject {
	// Create search and filter header as per fyne.md
	searchEntry := widget.NewEntry()
	searchEntry.SetPlaceHolder("Search containers...")

	filterSelect := widget.NewSelect([]string{"All", "Running", "Stopped"}, func(value string) {
		// TODO: Implement filtering
	})
	filterSelect.SetSelected("All")

	addBtn := cybertheme.CreatePrimaryButton("+ New", func() {
		// TODO: Implement container creation
	}, dv.theme)

	headerControls := container.NewBorder(
		nil, nil, // top, bottom
		container.NewHBox(widget.NewLabel("Search:"), searchEntry),
		container.NewHBox(widget.NewLabel("Filter:"), filterSelect, addBtn),
		nil, // center
	)

	// Create main layout: 40% Container List | 60% Container Details (as per fyne.md)
	containerList := dv.createContainerList()
	containerDetails := dv.createContainerDetails()

	// Use HSplit with 40/60 ratio as specified in fyne.md
	mainContent := container.NewHSplit(containerList, containerDetails)
	mainContent.SetOffset(0.4) // 40% for list, 60% for details

	// Combine header and main content
	return container.NewVBox(
		headerControls,
		widget.NewSeparator(),
		mainContent,
	)
}

// createContainerList creates the container list (40% of layout as per fyne.md)
func (dv *DockerView) createContainerList() fyne.CanvasObject {
	// Container list with status indicators as per fyne.md
	dv.containerList = widget.NewList(
		func() int { return len(dv.containers) },
		func() fyne.CanvasObject {
			// Create container item template following fyne.md design
			statusDot := widget.NewLabel("●")
			nameLabel := widget.NewLabel("Container Name")
			nameLabel.TextStyle.Bold = true
			statusLabel := widget.NewLabel("Status")
			actionBtn := widget.NewButton("▶️", nil)
			actionBtn.Resize(fyne.NewSize(30, 30))

			return container.NewVBox(
				container.NewHBox(statusDot, nameLabel, actionBtn),
				statusLabel,
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			if id >= len(dv.containers) {
				return
			}
			containerItem := dv.containers[id]
			vbox := obj.(*fyne.Container)
			topRow := vbox.Objects[0].(*fyne.Container)
			bottomRow := vbox.Objects[1].(*widget.Label)

			// Status dot with color coding
			statusDot := topRow.Objects[0].(*widget.Label)
			if containerItem.Status == "Running" {
				statusDot.SetText("● ")
			} else {
				statusDot.SetText("○ ")
			}

			// Container name and image
			nameLabel := topRow.Objects[1].(*widget.Label)
			nameLabel.SetText(fmt.Sprintf("%s:%s", containerItem.Name, containerItem.Image))

			// Action button (play/stop based on status)
			actionBtn := topRow.Objects[2].(*widget.Button)
			if containerItem.Status == "Running" {
				actionBtn.SetText("■")
				actionBtn.OnTapped = func() { dv.stopContainer() }
			} else {
				actionBtn.SetText("▶️")
				actionBtn.OnTapped = func() { dv.startContainer() }
			}

			// Status and time info
			timeAgo := time.Since(containerItem.Created)
			var timeStr string
			if timeAgo.Hours() < 1 {
				timeStr = fmt.Sprintf("%.0f minutes", timeAgo.Minutes())
			} else if timeAgo.Hours() < 24 {
				timeStr = fmt.Sprintf("%.0f hours", timeAgo.Hours())
			} else {
				timeStr = fmt.Sprintf("%.0f days", timeAgo.Hours()/24)
			}

			bottomRow.SetText(fmt.Sprintf("%s • %s", containerItem.Status, timeStr))
		},
	)

	// Set up selection handling
	dv.containerList.OnSelected = func(id widget.ListItemID) {
		dv.selectedContainer = id
		dv.updateContainerDetails()
	}

	return container.NewScroll(dv.containerList)
}

// createContainerDetails creates the container details panel (60% of layout as per fyne.md)
func (dv *DockerView) createContainerDetails() fyne.CanvasObject {
	// Container details header
	containerNameLabel := widget.NewLabel("Select a container")
	containerNameLabel.TextStyle.Bold = true

	containerStatusLabel := widget.NewLabel("")

	// Resource usage section as per fyne.md
	cpuLabel := widget.NewLabel("CPU:")
	cpuProgress := widget.NewProgressBar()
	cpuProgress.SetValue(0.45) // 45% as shown in fyne.md
	cpuPercentLabel := widget.NewLabel("45%")

	memLabel := widget.NewLabel("MEM:")
	memProgress := widget.NewProgressBar()
	memProgress.SetValue(0.72) // 72% as shown in fyne.md
	memPercentLabel := widget.NewLabel("72%")

	diskLabel := widget.NewLabel("DISK:")
	diskProgress := widget.NewProgressBar()
	diskProgress.SetValue(0.34) // 34% as shown in fyne.md
	diskPercentLabel := widget.NewLabel("34%")

	// Resource usage container
	resourceUsage := container.NewVBox(
		widget.NewLabel("Resource Usage"),
		container.NewHBox(cpuLabel, cpuProgress, cpuPercentLabel),
		container.NewHBox(memLabel, memProgress, memPercentLabel),
		container.NewHBox(diskLabel, diskProgress, diskPercentLabel),
	)

	// Logs section as per fyne.md
	logsLabel := widget.NewLabel("Logs")
	logsLabel.TextStyle.Bold = true

	dv.logViewer = widget.NewMultiLineEntry()
	dv.logViewer.SetText("Select a container to view logs...")
	dv.logViewer.Wrapping = fyne.TextWrapWord
	dv.logViewer.MultiLine = true

	logsContainer := container.NewVBox(
		logsLabel,
		container.NewScroll(dv.logViewer),
	)

	// Combine all details sections
	detailsContent := container.NewVBox(
		container.NewHBox(containerNameLabel, containerStatusLabel),
		widget.NewSeparator(),
		resourceUsage,
		widget.NewSeparator(),
		logsContainer,
	)

	return container.NewScroll(detailsContent)
}

// updateContainerDetails updates the details panel when a container is selected
func (dv *DockerView) updateContainerDetails() {
	if dv.selectedContainer < 0 || dv.selectedContainer >= len(dv.containers) {
		return
	}

	selectedContainer := dv.containers[dv.selectedContainer]

	// Update container details (this would be enhanced with real data)
	dv.logViewer.SetText(fmt.Sprintf(`Container: %s
Image: %s
Status: %s
Ports: %s
Created: %s

Recent logs:
2024-01-20 10:30:15 [INFO] Container started successfully
2024-01-20 10:30:16 [INFO] Application listening on port %s
2024-01-20 10:30:17 [INFO] Health check passed
2024-01-20 10:30:18 [INFO] Processing requests...`,
		selectedContainer.Name,
		selectedContainer.Image,
		selectedContainer.Status,
		selectedContainer.Ports,
		selectedContainer.Created.Format("2006-01-02 15:04:05"),
		selectedContainer.Ports,
	))
}

// createImageSection creates the image management interface
func (dv *DockerView) createImageSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Image Management")
	sectionHeader.TextStyle.Bold = true

	// Image list
	dv.imageList = widget.NewList(
		func() int { return len(dv.images) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Image"),
				widget.NewLabel("Tag"),
				widget.NewLabel("Size"),
				widget.NewLabel("Created"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			image := dv.images[id]
			hbox := obj.(*fyne.Container)

			// Update labels with safe ID slicing
			shortID := image.ID
			if len(image.ID) > 8 {
				shortID = image.ID[:8]
			}
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", image.Name, shortID))
			hbox.Objects[1].(*widget.Label).SetText(image.Tag)
			hbox.Objects[2].(*widget.Label).SetText(image.Size)
			hbox.Objects[3].(*widget.Label).SetText(image.Created.Format("2006-01-02 15:04"))
		},
	)
	dv.imageList.Resize(fyne.NewSize(800, 120))

	// Set up selection handling
	dv.imageList.OnSelected = func(id widget.ListItemID) {
		dv.selectedImage = id
	}

	// Image action buttons with enhanced styling and guaranteed white text
	pullBtn := cybertheme.CreatePrimaryButton("⬇ Pull", func() {
		dv.pullImage()
	}, dv.theme)

	buildBtn := cybertheme.CreateUnifiedButton("🔨 Build", func() {
		dv.buildImage()
	}, dv.theme, "default")

	pushBtn := cybertheme.CreateUnifiedButton("⬆ Push", func() {
		dv.pushImage()
	}, dv.theme, "default")

	removeImgBtn := cybertheme.CreateSecondaryButton("🗑 Remove", func() {
		dv.removeImage()
	}, dv.theme)

	imageButtons := container.NewHBox(pullBtn, buildBtn, pushBtn, removeImgBtn)

	return container.NewVBox(
		sectionHeader,
		dv.imageList,
		imageButtons,
	)
}

// createVolumeSection creates the volume management interface
func (dv *DockerView) createVolumeSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Volume Management")
	sectionHeader.TextStyle.Bold = true

	// Volume list
	dv.volumeList = widget.NewList(
		func() int { return len(dv.volumes) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Volume"),
				widget.NewLabel("Driver"),
				widget.NewLabel("Mountpoint"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			volume := dv.volumes[id]
			hbox := obj.(*fyne.Container)

			// Update labels
			hbox.Objects[0].(*widget.Label).SetText(volume.Name)
			hbox.Objects[1].(*widget.Label).SetText(volume.Driver)
			hbox.Objects[2].(*widget.Label).SetText(volume.Mountpoint)
		},
	)
	dv.volumeList.Resize(fyne.NewSize(800, 100))

	// Volume action buttons with enhanced styling
	createVolBtn := cybertheme.CreatePrimaryButton("➕ Create", func() {
		dv.createVolume()
	}, dv.theme)

	removeVolBtn := cybertheme.CreateSecondaryButton("🗑 Remove", func() {
		dv.removeVolume()
	}, dv.theme)

	volumeButtons := container.NewHBox(createVolBtn, removeVolBtn)

	return container.NewVBox(
		sectionHeader,
		dv.volumeList,
		volumeButtons,
	)
}

// createNetworkSection creates the network management interface
func (dv *DockerView) createNetworkSection() fyne.CanvasObject {
	// Section header
	sectionHeader := widget.NewLabel("Network Management")
	sectionHeader.TextStyle.Bold = true

	// Network list
	dv.networkList = widget.NewList(
		func() int { return len(dv.networks) },
		func() fyne.CanvasObject {
			return container.NewHBox(
				widget.NewLabel("Network"),
				widget.NewLabel("Driver"),
				widget.NewLabel("Scope"),
			)
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			network := dv.networks[id]
			hbox := obj.(*fyne.Container)

			// Update labels with safe ID slicing
			shortID := network.ID
			if len(network.ID) > 8 {
				shortID = network.ID[:8]
			}
			hbox.Objects[0].(*widget.Label).SetText(fmt.Sprintf("%s (%s)", network.Name, shortID))
			hbox.Objects[1].(*widget.Label).SetText(network.Driver)
			hbox.Objects[2].(*widget.Label).SetText(network.Scope)
		},
	)
	dv.networkList.Resize(fyne.NewSize(800, 100))

	// Network action buttons with enhanced styling
	createNetBtn := cybertheme.CreatePrimaryButton("🌐 Create", func() {
		dv.createNetwork()
	}, dv.theme)

	removeNetBtn := cybertheme.CreateSecondaryButton("🗑 Remove", func() {
		dv.removeNetwork()
	}, dv.theme)

	networkButtons := container.NewHBox(createNetBtn, removeNetBtn)

	return container.NewVBox(
		sectionHeader,
		dv.networkList,
		networkButtons,
	)
}

// createStatusSection creates the status display
func (dv *DockerView) createStatusSection() fyne.CanvasObject {
	dv.statusLabel = widget.NewLabel(">>> DOCKER MODULE READY <<<")
	dv.statusLabel.Alignment = fyne.TextAlignCenter
	dv.statusLabel.TextStyle.Bold = true

	return dv.statusLabel
}

// Docker operation methods
func (dv *DockerView) startContainer() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.currentContainer = container.Name
		dv.statusLabel.SetText(">>> STARTING CONTAINER <<<")
		dv.logViewer.SetText(fmt.Sprintf("Starting container: %s\n[INFO] Container started successfully", container.Name))
	}
}

func (dv *DockerView) stopContainer() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.statusLabel.SetText(">>> STOPPING CONTAINER <<<")
		dv.logViewer.SetText(fmt.Sprintf("Stopping container: %s\n[INFO] Container stopped gracefully", container.Name))
	}
}

func (dv *DockerView) restartContainer() {
	dv.statusLabel.SetText(">>> RESTARTING CONTAINER <<<")
}

func (dv *DockerView) viewContainerLogs() {
	if dv.selectedContainer >= 0 && dv.selectedContainer < len(dv.containers) {
		container := dv.containers[dv.selectedContainer]
		dv.logViewer.SetText(fmt.Sprintf("Logs for container: %s\n\n2024-01-20 10:30:15 [INFO] Application started\n2024-01-20 10:30:16 [INFO] Processing requests...", container.Name))
		dv.statusLabel.SetText(">>> VIEWING CONTAINER LOGS <<<")
	}
}

func (dv *DockerView) removeContainer() {
	dv.statusLabel.SetText(">>> REMOVING CONTAINER <<<")
}

func (dv *DockerView) pullImage() {
	dv.statusLabel.SetText(">>> PULLING IMAGE <<<")
}

func (dv *DockerView) buildImage() {
	dv.statusLabel.SetText(">>> BUILDING IMAGE <<<")
}

func (dv *DockerView) pushImage() {
	dv.statusLabel.SetText(">>> PUSHING IMAGE <<<")
}

func (dv *DockerView) removeImage() {
	dv.statusLabel.SetText(">>> REMOVING IMAGE <<<")
}

func (dv *DockerView) createVolume() {
	dv.statusLabel.SetText(">>> CREATING VOLUME <<<")
}

func (dv *DockerView) removeVolume() {
	dv.statusLabel.SetText(">>> REMOVING VOLUME <<<")
}

func (dv *DockerView) createNetwork() {
	dv.statusLabel.SetText(">>> CREATING NETWORK <<<")
}

func (dv *DockerView) removeNetwork() {
	dv.statusLabel.SetText(">>> REMOVING NETWORK <<<")
}

// Content returns the Docker view's content
func (dv *DockerView) Content() fyne.CanvasObject {
	return dv.container
}

// Refresh refreshes the Docker view
func (dv *DockerView) Refresh() {
	if dv.container != nil {
		dv.container.Refresh()
	}
}
