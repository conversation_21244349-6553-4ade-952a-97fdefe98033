// Package views provides the module navigation component following fyne.md specifications
package views

import (
	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// ModuleNavigation represents the horizontal module navigation (64px height as per fyne.md)
type ModuleNavigation struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *fyne.Container

	// Navigation components
	moduleButtons map[string]*widget.Button
	activeModule  string

	// Callback
	onModuleSelected func(string)
}

// ModuleInfo represents module information for navigation
type ModuleInfo struct {
	Name        string
	DisplayName string
	Icon        string
	Enabled     bool
}

// NewModuleNavigation creates a new module navigation following fyne.md specifications
func NewModuleNavigation(cfg *config.Config, theme *cybertheme.CyberTheme, onModuleSelected func(string)) (*ModuleNavigation, error) {
	nav := &ModuleNavigation{
		config:           cfg,
		theme:            theme,
		moduleButtons:    make(map[string]*widget.Button),
		onModuleSelected: onModuleSelected,
	}

	// Initialize components
	nav.initializeComponents()

	return nav, nil
}

// initializeComponents creates navigation components following fyne.md layout
func (mn *ModuleNavigation) initializeComponents() {
	// Define modules as per fyne.md specifications
	modules := []ModuleInfo{
		{Name: "docker", DisplayName: "Docker", Icon: "🐳", Enabled: true},
		{Name: "chat", DisplayName: "Chat", Icon: "🧠", Enabled: true},
		{Name: "database", DisplayName: "Database", Icon: "🗄️", Enabled: true},
		{Name: "kubernetes", DisplayName: "K8s", Icon: "☸️", Enabled: true},
		{Name: "mcp", DisplayName: "MCP", Icon: "🤖", Enabled: true},
		{Name: "search", DisplayName: "Search", Icon: "🔍", Enabled: true},
		{Name: "tasks", DisplayName: "Task", Icon: "📋", Enabled: true},
		{Name: "agent", DisplayName: "Agent", Icon: "🎯", Enabled: true},
	}

	// Create module buttons
	var buttons []fyne.CanvasObject
	for _, module := range modules {
		button := mn.createModuleButton(module)
		mn.moduleButtons[module.Name] = button
		buttons = append(buttons, button)
	}

	// Create horizontal container with proper spacing (Material Design 3)
	mn.container = container.NewHBox(buttons...)

	// Set fixed height as per fyne.md specifications (64px)
	mn.container.Resize(fyne.NewSize(0, 64))
}

// createModuleButton creates a module button with proper styling
func (mn *ModuleNavigation) createModuleButton(module ModuleInfo) *widget.Button {
	// Create button text with icon and name
	buttonText := module.Icon + "\n" + module.DisplayName

	// Create button with unified styling
	button := cybertheme.CreateUnifiedButton(buttonText, func() {
		mn.selectModule(module.Name)
	}, mn.theme, "navigation")

	// Apply module-specific styling
	if !module.Enabled {
		button.Disable()
	} else {
		button.Importance = widget.MediumImportance
	}

	// Set consistent size for navigation buttons
	button.Resize(fyne.NewSize(100, 56))

	return button
}

// selectModule handles module selection
func (mn *ModuleNavigation) selectModule(moduleName string) {
	// Update active module
	previousModule := mn.activeModule
	mn.activeModule = moduleName

	// Update button states
	mn.updateButtonStates(previousModule, moduleName)

	// Trigger callback
	if mn.onModuleSelected != nil {
		mn.onModuleSelected(moduleName)
	}
}

// updateButtonStates updates the visual state of navigation buttons
func (mn *ModuleNavigation) updateButtonStates(previousModule, activeModule string) {
	// Reset previous module button
	if previousModule != "" {
		if prevBtn, exists := mn.moduleButtons[previousModule]; exists {
			prevBtn.Importance = widget.MediumImportance
			prevBtn.Refresh()
		}
	}

	// Highlight active module button
	if activeBtn, exists := mn.moduleButtons[activeModule]; exists {
		activeBtn.Importance = widget.HighImportance
		activeBtn.Refresh()
	}
}

// SetActiveModule sets the active module programmatically
func (mn *ModuleNavigation) SetActiveModule(moduleName string) {
	if _, exists := mn.moduleButtons[moduleName]; exists {
		mn.selectModule(moduleName)
	}
}

// GetActiveModule returns the currently active module
func (mn *ModuleNavigation) GetActiveModule() string {
	return mn.activeModule
}

// EnableModule enables a specific module
func (mn *ModuleNavigation) EnableModule(moduleName string) {
	if button, exists := mn.moduleButtons[moduleName]; exists {
		button.Enable()
		button.Refresh()
	}
}

// DisableModule disables a specific module
func (mn *ModuleNavigation) DisableModule(moduleName string) {
	if button, exists := mn.moduleButtons[moduleName]; exists {
		button.Disable()
		button.Refresh()
	}
}

// SetModuleStatus updates the status indicator for a module
func (mn *ModuleNavigation) SetModuleStatus(moduleName, status string) {
	// This could be enhanced to show status indicators on buttons
	// For now, we'll use the existing button structure
}

// Content returns the navigation's content
func (mn *ModuleNavigation) Content() fyne.CanvasObject {
	return mn.container
}

// Refresh refreshes the navigation
func (mn *ModuleNavigation) Refresh() {
	if mn.container != nil {
		mn.container.Refresh()
	}
}

// AddModule adds a new module to the navigation
func (mn *ModuleNavigation) AddModule(module ModuleInfo) {
	if _, exists := mn.moduleButtons[module.Name]; !exists {
		button := mn.createModuleButton(module)
		mn.moduleButtons[module.Name] = button
		mn.container.Add(button)
		mn.container.Refresh()
	}
}

// RemoveModule removes a module from the navigation
func (mn *ModuleNavigation) RemoveModule(moduleName string) {
	if button, exists := mn.moduleButtons[moduleName]; exists {
		mn.container.Remove(button)
		delete(mn.moduleButtons, moduleName)
		mn.container.Refresh()
	}
}
