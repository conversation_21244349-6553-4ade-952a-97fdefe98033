// Package views provides the app header component following fyne.md specifications
package views

import (
	"fmt"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/pkg/cybertheme"
)

// AppHeader represents the main application header (56px height as per fyne.md)
type AppHeader struct {
	config    *config.Config
	theme     *cybertheme.CyberTheme
	container *fyne.Container

	// Header components
	logoLabel     *widget.Label
	titleLabel    *widget.Label
	statusLabel   *widget.Label
	settingsBtn   *widget.Button
	userBtn       *widget.Button
	timeLabel     *widget.Label

	// Callbacks
	onSettingsClicked func()
	onUserClicked     func()
}

// NewAppHeader creates a new app header following fyne.md specifications
func NewAppHeader(cfg *config.Config, theme *cybertheme.CyberTheme) (*AppHeader, error) {
	header := &AppHeader{
		config: cfg,
		theme:  theme,
	}

	// Initialize components
	header.initializeComponents()

	// Start time updater
	go header.updateTime()

	return header, nil
}

// initializeComponents creates header components following fyne.md layout
func (ah *AppHeader) initializeComponents() {
	// Logo and title (left side)
	ah.logoLabel = widget.NewLabel("🖥️")
	ah.logoLabel.TextStyle.Bold = true

	ah.titleLabel = widget.NewLabel(fmt.Sprintf("%s v%s", ah.config.App.Name, ah.config.App.Version))
	ah.titleLabel.TextStyle.Bold = true

	// Status indicator (center-left)
	ah.statusLabel = widget.NewLabel("🟢 ONLINE")
	ah.statusLabel.TextStyle.Bold = true

	// Settings button (right side)
	ah.settingsBtn = cybertheme.CreateCompactButton("⚙️ Settings", func() {
		if ah.onSettingsClicked != nil {
			ah.onSettingsClicked()
		}
	}, ah.theme)

	// User button (right side)
	ah.userBtn = cybertheme.CreateCompactButton("👤 User", func() {
		if ah.onUserClicked != nil {
			ah.onUserClicked()
		}
	}, ah.theme)

	// Time display (far right)
	ah.timeLabel = widget.NewLabel(time.Now().Format("15:04:05"))
	ah.timeLabel.TextStyle.Monospace = true

	// Create left section
	leftSection := container.NewHBox(
		ah.logoLabel,
		ah.titleLabel,
		widget.NewSeparator(),
		ah.statusLabel,
	)

	// Create right section
	rightSection := container.NewHBox(
		ah.settingsBtn,
		ah.userBtn,
		widget.NewSeparator(),
		ah.timeLabel,
	)

	// Create main container with border layout (56px height as per fyne.md)
	ah.container = container.NewBorder(
		nil, nil, // top, bottom
		leftSection, rightSection, // left, right
		nil, // center (empty for clean look)
	)

	// Set fixed height as per fyne.md specifications
	ah.container.Resize(fyne.NewSize(0, 56))
}

// updateTime continuously updates the time display
func (ah *AppHeader) updateTime() {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		currentTime := time.Now().Format("15:04:05")
		// Use proper Fyne threading
		fyne.Do(func() {
			ah.timeLabel.SetText(currentTime)
		})
	}
}

// SetStatus updates the status indicator
func (ah *AppHeader) SetStatus(status, level string) {
	var indicator string
	switch level {
	case "success":
		indicator = "🟢 " + status
	case "warning":
		indicator = "🟡 " + status
	case "error":
		indicator = "🔴 " + status
	case "info":
		indicator = "🔵 " + status
	case "processing":
		indicator = "🟣 " + status
	default:
		indicator = "🟢 " + status
	}

	fyne.Do(func() {
		ah.statusLabel.SetText(indicator)
	})
}

// SetOnSettingsClicked sets the settings button callback
func (ah *AppHeader) SetOnSettingsClicked(callback func()) {
	ah.onSettingsClicked = callback
}

// SetOnUserClicked sets the user button callback
func (ah *AppHeader) SetOnUserClicked(callback func()) {
	ah.onUserClicked = callback
}

// Content returns the header's content
func (ah *AppHeader) Content() fyne.CanvasObject {
	return ah.container
}

// Refresh refreshes the header
func (ah *AppHeader) Refresh() {
	if ah.container != nil {
		ah.container.Refresh()
	}
}
