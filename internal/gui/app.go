// Package gui provides the GUI implementation for Assistant-Go
// Maintains the beloved cyberpunk aesthetic while implementing modular architecture
package gui

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"

	"assistant-go/internal/config"
	"assistant-go/internal/gui/views"
	"assistant-go/pkg/cybertheme"
)

// App represents the GUI application
type App struct {
	// Fyne application instance
	fyneApp fyne.App
	window  fyne.Window

	// Configuration and theme
	config *config.Config
	theme  *cybertheme.CyberTheme

	// Views and components following fyne.md layout structure
	appHeader        *views.AppHeader
	moduleNavigation *views.ModuleNavigation
	mainView         *views.MainView
	statusBar        *views.StatusBar

	// Module interfaces - will be populated as modules are added
	modules map[string]ModuleUI

	// Application state
	currentModule string
	logger        *slog.Logger
}

// ModuleUI interface defines how modules integrate with the GUI
// Following Go philosophy: interfaces are discovered, not designed
type ModuleUI interface {
	// Name returns the module's display name
	Name() string

	// Icon returns the module's icon (optional)
	Icon() fyne.Resource

	// CreateContent creates the main content widget for this module
	CreateContent() fyne.CanvasObject

	// CreateSidebarItem creates the sidebar navigation item
	CreateSidebarItem() fyne.CanvasObject

	// OnActivate is called when the module becomes active
	OnActivate()

	// OnDeactivate is called when the module becomes inactive
	OnDeactivate()

	// Refresh updates the module's content
	Refresh() error
}

// New creates a new GUI application
func New(fyneApp fyne.App, cfg *config.Config, modules map[string]interface{}) (*App, error) {
	// Create the main window with cyberpunk styling
	window := fyneApp.NewWindow(cfg.App.Name)
	window.SetTitle(fmt.Sprintf("%s v%s - Cyberpunk Development Assistant",
		cfg.App.Name, cfg.App.Version))

	// Set window properties
	window.Resize(fyne.NewSize(1400, 900))
	window.CenterOnScreen()

	// Create cyberpunk theme
	theme := cybertheme.NewCustomCyberTheme(
		cfg.Theme.PrimaryColor,
		cfg.Theme.SecondaryColor,
		cfg.Theme.Background,
	)
	theme.SetFontSize(cfg.Theme.FontSize)

	// Initialize logger
	logger := slog.Default().With("component", "gui")

	app := &App{
		fyneApp: fyneApp,
		window:  window,
		config:  cfg,
		theme:   theme,
		modules: make(map[string]ModuleUI),
		logger:  logger,
	}

	// Initialize the GUI components
	if err := app.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize GUI components: %w", err)
	}

	// Setup the main layout
	app.setupMainLayout()

	logger.Info("GUI application created successfully")
	return app, nil
}

// initializeComponents initializes all GUI components following fyne.md layout
func (a *App) initializeComponents() error {
	a.logger.Info("Initializing GUI components following fyne.md specifications...")

	var err error

	// Create app header (56px height as per fyne.md)
	a.appHeader, err = views.NewAppHeader(a.config, a.theme)
	if err != nil {
		return fmt.Errorf("failed to create app header: %w", err)
	}

	// Create module navigation (64px height as per fyne.md)
	a.moduleNavigation, err = views.NewModuleNavigation(a.config, a.theme, a.onModuleSelected)
	if err != nil {
		return fmt.Errorf("failed to create module navigation: %w", err)
	}

	// Create main view (flex content area as per fyne.md)
	a.mainView, err = views.NewMainView(a.config, a.theme)
	if err != nil {
		return fmt.Errorf("failed to create main view: %w", err)
	}

	// Create status bar (32px height as per fyne.md)
	a.statusBar, err = views.NewStatusBar(a.config, a.theme)
	if err != nil {
		return fmt.Errorf("failed to create status bar: %w", err)
	}

	// Set up component callbacks
	a.setupComponentCallbacks()

	a.logger.Info("GUI components initialized successfully following fyne.md layout")
	return nil
}

// setupComponentCallbacks sets up callbacks between components
func (a *App) setupComponentCallbacks() {
	// Set up app header callbacks
	a.appHeader.SetOnSettingsClicked(func() {
		a.logger.Info("Settings clicked")
		// TODO: Implement settings dialog
	})

	a.appHeader.SetOnUserClicked(func() {
		a.logger.Info("User clicked")
		// TODO: Implement user menu
	})
}

// setupMainLayout creates the main application layout following fyne.md structure
func (a *App) setupMainLayout() {
	a.logger.Info("Setting up main layout following fyne.md specifications...")

	// Create the main layout following fyne.md structure:
	// +----------------------------------------------------------+
	// | App Header (56px)                                       |
	// +----------------------------------------------------------+
	// | Module Navigation (64px)                                |
	// +----------------------------------------------------------+
	// | Module Content Area (flex)                              |
	// +----------------------------------------------------------+
	// | Status Bar (32px)                                       |
	// +----------------------------------------------------------+

	mainContainer := container.NewBorder(
		// Top section: App Header + Module Navigation
		container.NewVBox(
			a.appHeader.Content(),
			widget.NewSeparator(),
			a.moduleNavigation.Content(),
		),
		// Bottom section: Status Bar
		a.statusBar.Content(),
		// Left and Right: None (full width layout)
		nil, nil,
		// Center: Main content area
		a.mainView.Content(),
	)

	// Set the content
	a.window.SetContent(mainContainer)

	// Add welcome message to status bar
	a.statusBar.SetMessage(">>> SYSTEM ONLINE - WELCOME BACK, KOOPA <<<", "success")

	a.logger.Info("Main layout setup complete following fyne.md structure")
}

// ShowAndRun displays the window and starts the GUI event loop
func (a *App) ShowAndRun() {
	a.logger.Info("Starting GUI event loop...")

	// Show startup message
	a.showStartupMessage()

	// Show the window and run
	a.window.ShowAndRun()
}

// showStartupMessage displays the cyberpunk startup sequence
func (a *App) showStartupMessage() {
	// Create startup dialog with cyberpunk styling
	startupText := widget.NewRichTextFromMarkdown(`
# 🖥️ CYBER TERMINAL ACTIVE

**SYSTEM STATUS:** INITIALIZING...
**CORE MODULES:** LOADING...
**AI CONSCIOUSNESS:** PENDING...

> Welcome back, Koopa. All systems are operational.
> Your development environment is ready.

**READY FOR DEVELOPMENT** 🚀
`)

	// Apply cyberpunk colors to the startup text
	startupText.Segments[0].(*widget.TextSegment).Style.ColorName = "primary"

	// Create a container with cyberpunk border
	startupContainer := container.NewVBox(
		startupText,
		widget.NewSeparator(),
		widget.NewLabel("Press any key to continue..."),
	)

	// Show as a modal dialog briefly
	dialog := widget.NewModalPopUp(startupContainer, a.window.Canvas())
	dialog.Resize(fyne.NewSize(500, 300))
	dialog.Show()

	// Auto-close after 2 seconds
	go func() {
		time.Sleep(2 * time.Second)
		// Fix threading issue: wrap UI update in fyne.Do()
		fyne.Do(func() {
			dialog.Hide()
		})
	}()
}

// RegisterModuleUI registers a module's UI components
func (a *App) RegisterModuleUI(moduleUI ModuleUI) error {
	name := moduleUI.Name()
	if _, exists := a.modules[name]; exists {
		return fmt.Errorf("module UI %s is already registered", name)
	}

	a.modules[name] = moduleUI

	// Add to module navigation
	// TODO: Implement module registration in navigation

	a.logger.Info("Module UI registered", "name", name)
	return nil
}

// onModuleSelected handles module selection from the sidebar
func (a *App) onModuleSelected(moduleName string) {
	a.logger.Info("Module selected", "name", moduleName)

	// Use the MainView's module switching functionality
	if err := a.mainView.SwitchToModule(moduleName); err != nil {
		a.logger.Error("Failed to switch to module", "name", moduleName, "error", err)

		// Show placeholder for unimplemented or failed modules
		a.showModulePlaceholder(moduleName)
		a.statusBar.SetMessage(fmt.Sprintf(">>> MODULE ERROR: %s <<<", moduleName), "error")
		return
	}

	// Update current module tracking
	a.currentModule = moduleName

	// Update module navigation active state
	a.moduleNavigation.SetActiveModule(moduleName)

	// Update app header status
	a.appHeader.SetStatus(fmt.Sprintf("MODULE: %s", strings.ToUpper(moduleName)), "success")

	// Update status bar with success message
	a.statusBar.SetMessage(fmt.Sprintf(">>> MODULE ACTIVE: %s <<<", strings.ToUpper(moduleName)), "success")

	a.logger.Info("Module switched successfully", "name", moduleName)
}

// showModulePlaceholder shows a placeholder for modules not yet implemented
func (a *App) showModulePlaceholder(moduleName string) {
	placeholderText := widget.NewRichTextFromMarkdown(fmt.Sprintf(`
# 🚧 MODULE: %s

**STATUS:** UNDER DEVELOPMENT
**PROGRESS:** ARCHITECTURE COMPLETE
**ETA:** NEXT PHASE

> This module is part of the modular refactoring plan.
> Implementation follows the Architecture.md specifications.

## Planned Features:
- Modern Go 1.24+ implementation
- Cyberpunk aesthetic integration
- Full functionality preservation
- Zero breaking changes

**//TODO: Complete implementation in next development phase**
`, moduleName))

	// Create container with cyberpunk styling
	placeholder := container.NewVBox(
		placeholderText,
		widget.NewSeparator(),
		widget.NewLabel(">>> DEVELOPMENT IN PROGRESS <<<"),
	)

	a.mainView.SetContent(placeholder)
	a.statusBar.SetMessage(fmt.Sprintf(">>> MODULE PLACEHOLDER: %s <<<", moduleName), "warning")
}

// SetTheme updates the application theme
func (a *App) SetTheme(theme *cybertheme.CyberTheme) {
	a.theme = theme
	theme.ApplyToApp(a.fyneApp)

	// Refresh all components
	a.refresh()
}

// refresh refreshes all GUI components
func (a *App) refresh() {
	// Refresh app header
	if a.appHeader != nil {
		a.appHeader.Refresh()
	}

	// Refresh module navigation
	if a.moduleNavigation != nil {
		a.moduleNavigation.Refresh()
	}

	// Refresh main view
	if a.mainView != nil {
		a.mainView.Refresh()
	}

	// Refresh status bar
	if a.statusBar != nil {
		a.statusBar.Refresh()
	}

	// Refresh all module UIs
	for _, moduleUI := range a.modules {
		moduleUI.Refresh()
	}

	// Refresh window
	a.window.Content().Refresh()
}

// GetWindow returns the main window
func (a *App) GetWindow() fyne.Window {
	return a.window
}

// GetTheme returns the current theme
func (a *App) GetTheme() *cybertheme.CyberTheme {
	return a.theme
}

// ShowNotification displays a cyberpunk-styled notification
func (a *App) ShowNotification(title, message, level string) {
	// Create notification with appropriate cyberpunk styling
	notification := fyne.NewNotification(title, message)

	// Set icon based on level
	switch level {
	case "success":
		// TODO: Add success icon
	case "warning":
		// TODO: Add warning icon
	case "error":
		// TODO: Add error icon
	default:
		// TODO: Add info icon
	}

	a.fyneApp.SendNotification(notification)

	// Also update status bar
	a.statusBar.SetMessage(fmt.Sprintf("%s: %s", title, message), level)
}

// Shutdown gracefully shuts down the GUI
func (a *App) Shutdown(ctx context.Context) error {
	a.logger.Info("Shutting down GUI...")

	// Deactivate current module
	if a.currentModule != "" {
		if module, exists := a.modules[a.currentModule]; exists {
			module.OnDeactivate()
		}
	}

	// Close the window
	a.window.Close()

	a.logger.Info("GUI shutdown complete")
	return nil
}
